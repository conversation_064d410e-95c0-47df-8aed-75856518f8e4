<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广告拦截检测测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-element {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #007bff;
            text-align: center;
            background: #f8f9fa;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        
        /* 广告拦截检测样式 */
        .adblock-notification {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-in-out;
            padding: 20px;
            box-sizing: border-box;
        }

        .adblock-notification.show {
            opacity: 1;
            visibility: visible;
        }

        .adblock-notification-content {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
            transform: translateY(-20px);
            transition: transform 0.3s ease-in-out;
            border: 1px solid #e1e5e9;
        }

        .adblock-notification.show .adblock-notification-content {
            transform: translateY(0);
        }

        .adblock-notification-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 24px 0;
            border-bottom: 1px solid #e1e5e9;
            margin-bottom: 20px;
        }

        .adblock-notification-header h4 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: #333333;
            display: flex;
            align-items: center;
        }

        .adblock-notification-header h4::before {
            content: "🚫";
            margin-right: 8px;
            font-size: 1.1em;
        }

        .adblock-notification-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666666;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            line-height: 1;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .adblock-notification-close:hover {
            background-color: #f5f5f5;
            color: #333333;
        }

        .adblock-notification-body {
            padding: 0 24px 24px;
        }

        .adblock-notification-body p {
            margin: 0 0 20px;
            line-height: 1.6;
            color: #333333;
            font-size: 0.95rem;
        }

        .adblock-notification-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .adblock-notification-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
            min-width: 120px;
        }

        .adblock-notification-btn.primary {
            background-color: #007bff;
            color: #ffffff;
        }

        .adblock-notification-btn.primary:hover {
            background-color: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .adblock-notification-btn.secondary {
            background-color: transparent;
            color: #333333;
            border: 1px solid #e1e5e9;
        }

        .adblock-notification-btn.secondary:hover {
            background-color: #f5f5f5;
            border-color: #d1d5d9;
        }
    </style>
</head>
<body>
    <h1>🧪 广告拦截检测测试页面</h1>
    
    <div class="status warning">
        <strong>测试说明：</strong>
        <ul>
            <li>如果您没有启用广告拦截器，应该不会看到弹窗</li>
            <li>如果您启用了广告拦截器（如 uBlock Origin、AdBlock Plus），应该会在 2-3 秒后看到弹窗</li>
        </ul>
    </div>

    <h2>📊 检测状态</h2>
    <div id="detection-status" class="status">检测中...</div>

    <h2>🎯 测试元素</h2>
    <p>下面这些元素会被广告拦截器隐藏：</p>

    <div class="adsbox test-element">
        <strong>测试元素 1</strong><br>
        类名: adsbox<br>
        如果您启用了广告拦截器，这个元素可能会被隐藏
    </div>

    <div class="advertisement test-element">
        <strong>测试元素 2</strong><br>
        类名: advertisement<br>
        如果您启用了广告拦截器，这个元素可能会被隐藏
    </div>

    <div class="pub_300x250 test-element">
        <strong>测试元素 3</strong><br>
        类名: pub_300x250<br>
        如果您启用了广告拦截器，这个元素可能会被隐藏
    </div>

    <h2>🔧 调试信息</h2>
    <div id="debug-info" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px;"></div>

    <!-- Google AdSense 脚本 -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5455845858903578" crossorigin="anonymous"></script>

    <script>
        // 简化的广告拦截检测脚本
        class SimpleAdBlockDetector {
            constructor() {
                this.detectionMethods = [];
                this.debugInfo = document.getElementById('debug-info');
                this.statusDiv = document.getElementById('detection-status');
                this.log('开始广告拦截检测...');
                this.init();
            }

            log(message) {
                console.log(message);
                if (this.debugInfo) {
                    this.debugInfo.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
                }
            }

            init() {
                this.log('初始化检测器...');
                setTimeout(() => this.runDetection(), 500);
            }

            runDetection() {
                this.log('开始运行检测方法...');
                this.testElementDetection();
                this.testGoogleAdsScript();
                this.testNetworkRequests();
                
                setTimeout(() => this.checkResults(), 3000);
            }

            testElementDetection() {
                this.log('测试元素检测...');
                const testElement = document.createElement('div');
                testElement.innerHTML = '&nbsp;';
                testElement.className = 'adsbox ad-banner advertisement ads pub_300x250';
                testElement.style.cssText = 'width: 1px !important; height: 1px !important; position: absolute !important; left: -10000px !important; top: -1000px !important;';
                
                document.body.appendChild(testElement);
                
                setTimeout(() => {
                    const rect = testElement.getBoundingClientRect();
                    const isHidden = rect.height === 0 || rect.width === 0 || 
                                   window.getComputedStyle(testElement).display === 'none' ||
                                   window.getComputedStyle(testElement).visibility === 'hidden';
                    
                    if (isHidden) {
                        this.log('✅ 元素检测: 广告元素被隐藏');
                        this.detectionMethods.push('element');
                    } else {
                        this.log('❌ 元素检测: 广告元素可见');
                    }
                    
                    document.body.removeChild(testElement);
                }, 100);
            }

            testGoogleAdsScript() {
                this.log('测试 Google Ads 脚本...');
                setTimeout(() => {
                    if (typeof window.adsbygoogle === 'undefined') {
                        this.log('✅ 脚本检测: adsbygoogle 未定义');
                        this.detectionMethods.push('script');
                    } else {
                        this.log('❌ 脚本检测: adsbygoogle 已定义');
                    }
                }, 1000);
            }

            testNetworkRequests() {
                this.log('测试网络请求...');
                const img = new Image();
                img.onload = () => {
                    this.log('❌ 网络检测: 广告请求成功');
                };
                img.onerror = () => {
                    this.log('✅ 网络检测: 广告请求被阻止');
                    this.detectionMethods.push('network');
                };
                img.src = 'https://pagead2.googlesyndication.com/pagead/show_ads.js?' + Math.random();
            }

            checkResults() {
                this.log('检查检测结果...');
                this.log('检测到的方法: ' + this.detectionMethods.join(', '));
                
                if (this.detectionMethods.length >= 1) {
                    this.log('🚫 检测到广告拦截器！显示通知...');
                    this.statusDiv.className = 'status error';
                    this.statusDiv.textContent = '检测到广告拦截器！';
                    this.showNotification();
                } else {
                    this.log('✅ 未检测到广告拦截器');
                    this.statusDiv.className = 'status success';
                    this.statusDiv.textContent = '未检测到广告拦截器';
                }
            }

            showNotification() {
                const notification = this.createNotificationElement();
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.classList.add('show');
                }, 100);

                this.addEventListeners(notification);
            }

            createNotificationElement() {
                const notification = document.createElement('div');
                notification.className = 'adblock-notification';
                notification.innerHTML = `
                    <div class="adblock-notification-content">
                        <div class="adblock-notification-header">
                            <h4>检测到广告拦截器</h4>
                            <button class="adblock-notification-close" aria-label="Close">&times;</button>
                        </div>
                        <div class="adblock-notification-body">
                            <p>请允许我们网站的广告。看起来您正在使用广告拦截器。我们依靠广告来资助网站运营并提供优质内容。</p>
                            <div class="adblock-notification-actions">
                                <button class="adblock-notification-btn primary">我知道了</button>
                                <button class="adblock-notification-btn secondary">禁用后刷新页面</button>
                            </div>
                        </div>
                    </div>
                `;
                return notification;
            }

            addEventListeners(notification) {
                const closeBtn = notification.querySelector('.adblock-notification-close');
                const understandBtn = notification.querySelector('.adblock-notification-btn.primary');
                const refreshBtn = notification.querySelector('.adblock-notification-btn.secondary');

                const closeNotification = () => {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                };

                closeBtn.addEventListener('click', closeNotification);
                understandBtn.addEventListener('click', closeNotification);
                refreshBtn.addEventListener('click', () => {
                    window.location.reload();
                });

                notification.addEventListener('click', (e) => {
                    if (e.target === notification) {
                        closeNotification();
                    }
                });
            }
        }

        // 启动检测器
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleAdBlockDetector();
        });
    </script>
</body>
</html>

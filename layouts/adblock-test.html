{{- define "title" }}Ad Blocker Detection Test - {{ .Site.Title }}{{ end -}}

{{- define "content" -}}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <article class="post">
                <header class="post-header">
                    <h1 class="post-title">Ad Blocker Detection Test</h1>
                    <div class="post-meta">
                        <span>Test page for ad blocker detection functionality</span>
                    </div>
                </header>

                <div class="post-content">
                    <div class="alert alert-info">
                        <h4>🧪 Ad Blocker Detection Test</h4>
                        <p>This page is designed to test the ad blocker detection functionality. Here's what should happen:</p>
                        <ul>
                            <li><strong>With Ad Blocker:</strong> You should see a polite notification asking to disable the ad blocker</li>
                            <li><strong>Without Ad Blocker:</strong> No notification should appear</li>
                        </ul>
                    </div>

                    <h2>How It Works</h2>
                    <p>The ad blocker detection system uses multiple methods to detect if ads are being blocked:</p>
                    
                    <h3>1. Element Detection</h3>
                    <p>Creates test elements with ad-related class names and checks if they get hidden or removed by ad blockers.</p>
                    
                    <h3>2. Script Loading Detection</h3>
                    <p>Monitors whether Google Ads scripts load successfully.</p>
                    
                    <h3>3. Network Request Detection</h3>
                    <p>Tests if requests to ad servers are blocked.</p>

                    <h2>Features</h2>
                    <ul>
                        <li>✅ <strong>Bilingual Support:</strong> Messages in Chinese and English</li>
                        <li>✅ <strong>Session-based:</strong> Shows notification only once per session</li>
                        <li>✅ <strong>Non-intrusive:</strong> Polite message that doesn't break functionality</li>
                        <li>✅ <strong>Theme-aware:</strong> Matches your site's design and dark/light theme</li>
                        <li>✅ <strong>Mobile-friendly:</strong> Responsive design for all devices</li>
                        <li>✅ <strong>Accessible:</strong> Keyboard navigation and screen reader support</li>
                    </ul>

                    <h2>Test Elements</h2>
                    <p>Below are some test elements that ad blockers typically hide:</p>
                    
                    <!-- Test ad elements that ad blockers typically block -->
                    <div class="test-ads-section">
                        <div class="adsbox" style="width: 300px; height: 250px; border: 2px dashed #ccc; margin: 20px 0; padding: 20px; text-align: center;">
                            <p>Test Ad Element 1<br><small>(Should be hidden by ad blockers)</small></p>
                        </div>
                        
                        <div class="advertisement" style="width: 728px; height: 90px; border: 2px dashed #ccc; margin: 20px 0; padding: 20px; text-align: center; max-width: 100%;">
                            <p>Test Ad Element 2<br><small>(Should be hidden by ad blockers)</small></p>
                        </div>
                        
                        <div class="pub_300x250" style="width: 300px; height: 250px; border: 2px dashed #ccc; margin: 20px 0; padding: 20px; text-align: center;">
                            <p>Test Ad Element 3<br><small>(Should be hidden by ad blockers)</small></p>
                        </div>
                    </div>

                    <h2>Manual Testing</h2>
                    <p>To test the ad blocker detection:</p>
                    <ol>
                        <li><strong>With Ad Blocker:</strong> Visit this page with an ad blocker enabled (uBlock Origin, AdBlock Plus, etc.)</li>
                        <li><strong>Without Ad Blocker:</strong> Disable your ad blocker and refresh the page</li>
                        <li><strong>Session Test:</strong> The notification should only appear once per browser session</li>
                    </ol>

                    <div class="alert alert-warning">
                        <h4>⚠️ Note</h4>
                        <p>This is a test page. The actual ad blocker detection will work on all pages of your site where Google Ads are enabled.</p>
                    </div>

                    <h2>Configuration</h2>
                    <p>The ad blocker detection is automatically enabled when you have Google AdSense configured in your <code>hugo.yaml</code>:</p>
                    <pre><code>params:
  analytics:
    adsense:
      enable: true
      client: "ca-pub-your-client-id"</code></pre>

                    <h2>Customization</h2>
                    <p>You can customize the detection behavior by modifying:</p>
                    <ul>
                        <li><code>themes/LoveIt/assets/js/adblock-detector.js</code> - Detection logic</li>
                        <li><code>themes/LoveIt/assets/css/adblock-notification.scss</code> - Notification styles</li>
                        <li><code>themes/LoveIt/i18n/</code> - Translation strings</li>
                    </ul>
                </div>
            </article>
        </div>
    </div>
</div>
{{- end -}}

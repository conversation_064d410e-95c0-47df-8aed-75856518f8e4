/**
 * Ad Blocker Notification Styles for <PERSON> Love<PERSON>t Theme
 * Responsive and theme-aware notification design
 */

.adblock-notification {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    padding: 20px;
    box-sizing: border-box;

    &.show {
        opacity: 1;
        visibility: visible;
    }

    .adblock-notification-content {
        background: var(--color-background, #ffffff);
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        max-width: 500px;
        width: 100%;
        max-height: 90vh;
        overflow-y: auto;
        transform: translateY(-20px);
        transition: transform 0.3s ease-in-out;
        border: 1px solid var(--color-border, #e1e5e9);

        [theme="dark"] & {
            background: var(--color-background-dark, #1a1a1a);
            border-color: var(--color-border-dark, #3a3a3a);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
    }

    &.show .adblock-notification-content {
        transform: translateY(0);
    }

    .adblock-notification-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 24px 0;
        border-bottom: 1px solid var(--color-border, #e1e5e9);
        margin-bottom: 20px;

        [theme="dark"] & {
            border-bottom-color: var(--color-border-dark, #3a3a3a);
        }

        h4 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--color-text, #333333);
            display: flex;
            align-items: center;

            [theme="dark"] & {
                color: var(--color-text-dark, #ffffff);
            }

            &::before {
                content: "🚫";
                margin-right: 8px;
                font-size: 1.1em;
            }
        }
    }

    .adblock-notification-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: var(--color-text-muted, #666666);
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;
        line-height: 1;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
            background-color: var(--color-hover, #f5f5f5);
            color: var(--color-text, #333333);
        }

        [theme="dark"] & {
            color: var(--color-text-muted-dark, #999999);

            &:hover {
                background-color: var(--color-hover-dark, #2a2a2a);
                color: var(--color-text-dark, #ffffff);
            }
        }
    }

    .adblock-notification-body {
        padding: 0 24px 24px;

        p {
            margin: 0 0 20px;
            line-height: 1.6;
            color: var(--color-text, #333333);
            font-size: 0.95rem;

            [theme="dark"] & {
                color: var(--color-text-dark, #ffffff);
            }
        }
    }

    .adblock-notification-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;

        @media (max-width: 480px) {
            flex-direction: column;
        }
    }

    .adblock-notification-btn {
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        flex: 1;
        min-width: 120px;

        &.primary {
            background-color: var(--color-primary, #007bff);
            color: #ffffff;

            &:hover {
                background-color: var(--color-primary-dark, #0056b3);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            }

            &:active {
                transform: translateY(0);
            }
        }

        &.secondary {
            background-color: transparent;
            color: var(--color-text, #333333);
            border: 1px solid var(--color-border, #e1e5e9);

            &:hover {
                background-color: var(--color-hover, #f5f5f5);
                border-color: var(--color-border-hover, #d1d5d9);
            }

            [theme="dark"] & {
                color: var(--color-text-dark, #ffffff);
                border-color: var(--color-border-dark, #3a3a3a);

                &:hover {
                    background-color: var(--color-hover-dark, #2a2a2a);
                    border-color: var(--color-border-hover-dark, #4a4a4a);
                }
            }
        }

        @media (max-width: 480px) {
            min-width: auto;
            flex: none;
        }
    }
}

// Animation for mobile devices
@media (max-width: 768px) {
    .adblock-notification {
        padding: 16px;

        .adblock-notification-content {
            border-radius: 8px;
        }

        .adblock-notification-header {
            padding: 16px 20px 0;
            margin-bottom: 16px;

            h4 {
                font-size: 1.1rem;
            }
        }

        .adblock-notification-body {
            padding: 0 20px 20px;

            p {
                font-size: 0.9rem;
            }
        }
    }
}

// High contrast mode support
@media (prefers-contrast: high) {
    .adblock-notification {
        .adblock-notification-content {
            border-width: 2px;
        }

        .adblock-notification-btn {
            border-width: 2px;

            &.primary {
                border: 2px solid var(--color-primary, #007bff);
            }
        }
    }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
    .adblock-notification,
    .adblock-notification-content,
    .adblock-notification-btn {
        transition: none;
    }

    .adblock-notification-btn:hover {
        transform: none;
    }
}

// Print styles
@media print {
    .adblock-notification {
        display: none !important;
    }
}

/**
 * Ad Blocker Detection for Hugo LoveIt Theme
 * Detects ad blockers using multiple methods and displays a polite message
 */

class AdBlockDetector {
    constructor() {
        this.isAdBlockDetected = false;
        this.sessionKey = 'adblock_notification_shown';
        this.detectionMethods = [];
        this.init();
    }

    init() {
        // Only run detection if notification hasn't been shown this session
        if (sessionStorage.getItem(this.sessionKey)) {
            return;
        }

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.runDetection());
        } else {
            this.runDetection();
        }
    }

    runDetection() {
        // Method 1: Test element detection
        this.testElementDetection();
        
        // Method 2: Google Ads script detection
        this.testGoogleAdsScript();
        
        // Method 3: Network request detection
        this.testNetworkRequests();
        
        // Check results after a short delay
        setTimeout(() => this.checkResults(), 2000);
    }

    testElementDetection() {
        const testElement = document.createElement('div');
        testElement.innerHTML = '&nbsp;';
        testElement.className = 'adsbox ad-banner advertisement ads pub_300x250 pub_300x250m pub_728x90 text-ad textAd text_ad text_ads text-ads text-ad-links';
        testElement.style.cssText = 'width: 1px !important; height: 1px !important; position: absolute !important; left: -10000px !important; top: -1000px !important;';
        
        document.body.appendChild(testElement);
        
        setTimeout(() => {
            const rect = testElement.getBoundingClientRect();
            const isHidden = rect.height === 0 || rect.width === 0 || 
                           window.getComputedStyle(testElement).display === 'none' ||
                           window.getComputedStyle(testElement).visibility === 'hidden';
            
            if (isHidden) {
                this.detectionMethods.push('element');
            }
            
            document.body.removeChild(testElement);
        }, 100);
    }

    testGoogleAdsScript() {
        // Check if Google Ads script failed to load
        const adsScript = document.querySelector('script[src*="googlesyndication.com"]');
        if (adsScript) {
            adsScript.onerror = () => {
                this.detectionMethods.push('script');
            };
        }

        // Check if adsbygoogle array is blocked
        setTimeout(() => {
            if (typeof window.adsbygoogle === 'undefined' || 
                (window.adsbygoogle && window.adsbygoogle.loaded === undefined)) {
                // Additional check for Google Ads
                if (document.querySelector('script[src*="googlesyndication.com"]')) {
                    this.detectionMethods.push('adsbygoogle');
                }
            }
        }, 1000);
    }

    testNetworkRequests() {
        // Create a fake ad request to test if it gets blocked
        const img = new Image();
        img.onload = () => {
            // If this loads, ads might not be blocked
        };
        img.onerror = () => {
            this.detectionMethods.push('network');
        };
        img.src = 'https://pagead2.googlesyndication.com/pagead/show_ads.js?' + Math.random();
    }

    checkResults() {
        if (this.detectionMethods.length >= 2) {
            this.isAdBlockDetected = true;
            this.showNotification();
        }
    }

    showNotification() {
        // Mark as shown for this session
        sessionStorage.setItem(this.sessionKey, 'true');

        // Get translations based on current language
        const lang = document.documentElement.lang || 'en';
        const translations = this.getTranslations(lang);

        // Create notification HTML
        const notification = this.createNotificationElement(translations);
        
        // Add to page
        document.body.appendChild(notification);
        
        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Add event listeners
        this.addEventListeners(notification);
    }

    getTranslations(lang) {
        const translations = {
            'zh-CN': {
                title: '检测到广告拦截器',
                message: '请允许我们网站的广告。看起来您正在使用广告拦截器。我们依靠广告来资助网站运营并提供优质内容。',
                button: '我知道了',
                refresh: '禁用后刷新页面'
            },
            'en': {
                title: 'Ad Blocker Detected',
                message: 'Please allow ads on our site. Looks like you\'re using an ad blocker. We rely on advertising to help fund our site and provide quality content.',
                button: 'I understand',
                refresh: 'Refresh page after disabling'
            }
        };

        return translations[lang] || translations['en'];
    }

    createNotificationElement(translations) {
        const notification = document.createElement('div');
        notification.className = 'adblock-notification';
        notification.innerHTML = `
            <div class="adblock-notification-content">
                <div class="adblock-notification-header">
                    <h4>${translations.title}</h4>
                    <button class="adblock-notification-close" aria-label="Close">&times;</button>
                </div>
                <div class="adblock-notification-body">
                    <p>${translations.message}</p>
                    <div class="adblock-notification-actions">
                        <button class="adblock-notification-btn primary">${translations.button}</button>
                        <button class="adblock-notification-btn secondary">${translations.refresh}</button>
                    </div>
                </div>
            </div>
        `;

        return notification;
    }

    addEventListeners(notification) {
        const closeBtn = notification.querySelector('.adblock-notification-close');
        const understandBtn = notification.querySelector('.adblock-notification-btn.primary');
        const refreshBtn = notification.querySelector('.adblock-notification-btn.secondary');

        const closeNotification = () => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        };

        closeBtn.addEventListener('click', closeNotification);
        understandBtn.addEventListener('click', closeNotification);
        refreshBtn.addEventListener('click', () => {
            window.location.reload();
        });

        // Close on outside click
        notification.addEventListener('click', (e) => {
            if (e.target === notification) {
                closeNotification();
            }
        });

        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeNotification();
            }
        });
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new AdBlockDetector();
    });
} else {
    new AdBlockDetector();
}

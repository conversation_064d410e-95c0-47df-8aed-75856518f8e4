{{- /* Ad Blocker Detection Partial for <PERSON>t Theme */ -}}
{{- $params := .Scratch.Get "params" -}}
{{- $analytics := .Scratch.Get "analytics" | default dict -}}
{{- $adsense := $analytics.adsense | default dict -}}

{{- /* Only load ad blocker detection if ads are enabled */ -}}
{{- if or $adsense.enable .Site.Params.analytics.adsense.enable -}}
    {{- $fingerprint := .Scratch.Get "fingerprint" -}}

    {{- /* Load ad blocker detection CSS */ -}}
    {{- dict "Source" "css/adblock-notification.scss" "ToCSS" (dict "outputStyle" "compressed") "Fingerprint" $fingerprint | partial "plugin/style.html" -}}

    {{- /* Load ad blocker detection JavaScript */ -}}
    {{- dict "Source" "js/adblock-detector.js" "Minify" true "Fingerprint" $fingerprint | partial "plugin/script.html" -}}

    {{- /* Add configuration for ad blocker detection */ -}}
    {{- $adBlockConfig := dict -}}
    {{- $adBlockConfig = dict "enabled" true | merge $adBlockConfig -}}
    {{- $adBlockConfig = dict "sessionBased" true | merge $adBlockConfig -}}
    {{- $adBlockConfig = dict "detectionMethods" (slice "element" "script" "network") | merge $adBlockConfig -}}
    {{- $adBlockConfig = dict "language" .Site.Language.Lang | merge $adBlockConfig -}}

    {{- /* Add to global config */ -}}
    {{- $config := .Scratch.Get "config" | default dict -}}
    {{- $config = dict "adBlockDetection" $adBlockConfig | merge $config -}}
    {{- .Scratch.Set "config" $config -}}
{{- end -}}

{{- $params := .Scratch.Get "params" -}}
{{- $cdn := .Scratch.Get "cdn" | default dict -}}
{{- $fingerprint := .Scratch.Get "fingerprint" -}}
{{- $config := (.Scratch.Get "this").config -}}

{{- /* Search */ -}}
{{- if .Site.Params.search | and .Site.Params.search.enable -}}
    {{- $search := .Site.Params.search -}}
    {{- $source := $cdn.autocompleteJS | default "lib/autocomplete/autocomplete.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- $config = dict "maxResultLength" $search.maxResultLength "snippetLength" $search.snippetLength "highlightTag" $search.highlightTag "noResultsFound" (T "noResultsFound") | dict "search" | merge $config -}}
    {{- if eq $search.type "lunr" -}}
        {{- with .Site.Home.OutputFormats.Get "json" -}}
            {{- $config = dict "type" "lunr" "lunrIndexURL" .RelPermalink | dict "search" | merge $config -}}
        {{- end -}}
        {{- $source := $cdn.lunrJS | default "lib/lunr/lunr.min.js" -}}
        {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
        {{- if T "lunrLanguageLib" -}}
            {{- $config = T "lunrLanguageCode" | dict "lunrLanguageCode" | dict "search" | merge $config -}}
            {{- with T "lunrSegmentitLib" -}}
                {{- $config = dict "lunrSegmentitURL" (resources.Get .).RelPermalink | dict "search" | merge $config -}}
            {{- end -}}
            {{- dict "Source" "lib/lunr/lunr.stemmer.support.js" "Minify" true "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
            {{- dict "Source" (T "lunrLanguageLib") "Minify" true "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
        {{- end -}}
    {{- else if eq $search.type "algolia" -}}
        {{- $source := $cdn.algoliasearchJS | default "lib/algoliasearch/lite/browser.umd.js" -}}
        {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
        {{- $config = dict "type" "algolia" "algoliaIndex" $search.algolia.index "algoliaAppID" $search.algolia.appID "algoliaSearchKey" $search.algolia.searchKey | dict "search" | merge $config -}}
    {{- end -}}
{{- end -}}

{{- /* lazysizes */ -}}
{{- $source := $cdn.lazysizesJS | default "lib/lazysizes/lazysizes.min.js" -}}
{{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}

{{- /* twemoji */ -}}
{{- if $params.twemoji -}}
    {{- $source := $cdn.twemojiJS | default "lib/twemoji/twemoji.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- $config = dict "twemoji" true | merge $config -}}
{{- end -}}

{{- /* lightgallery */ -}}
{{- if $params.lightgallery -}}
    {{- $source := $cdn.lightgalleryCSS | default "lib/lightgallery/css/lightgallery-bundle.min.css" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/style.html" -}}
    {{- if not $cdn.lightgalleryCSS -}}
        {{- (resources.Get "lib/lightgallery/images/loading.gif").Publish -}}
        {{- range resources.Match "lib/lightgallery/fonts/*" -}}
            {{- .Publish -}}
        {{- end -}}
    {{- end -}}
    {{- $source := $cdn.lightgalleryJS | default "lib/lightgallery/lightgallery.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- $source := $cdn.lightgalleryThumbnailJS | default "lib/lightgallery/plugins/thumbnail/lg-thumbnail.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- $source := $cdn.lightgalleryZoomJS | default "lib/lightgallery/plugins/zoom/lg-zoom.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- $config = dict "lightgallery" true | merge $config -}}
{{- end -}}

{{- /* clipboard.js */ -}}
{{- if $params.code.copy | default true -}}
    {{- $source := $cdn.clipboardJS | default "lib/clipboard/clipboard.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
{{- end -}}

{{- /* Sharer.js */ -}}
{{- if $params.share.enable -}}
    {{- $source := $cdn.sharerJS | default "lib/sharer/sharer.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
{{- end -}}

{{- /* TypeIt */ -}}
{{- with (.Scratch.Get "this").typeitMap -}}
    {{- $typeit := $.Site.Params.typeit -}}
    {{- $source := $cdn.typeitJS | default "lib/typeit/index.umd.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" $.Scratch "Data" | partial "scratch/script.html" -}}
    {{- $config = dict "speed" $typeit.speed "cursorSpeed" $typeit.cursorSpeed "cursorChar" $typeit.cursorChar "duration" $typeit.duration "data" . | dict "typeit" | merge $config -}}
{{- end -}}

{{- /* KaTeX */ -}}
{{- $math := $params.math -}}
{{- if eq $math true -}}
    {{- $math = .Site.Params.page.math | default dict -}}
{{- else if eq $math false -}}
    {{- $math = dict "enable" false -}}
{{- end -}}
{{- if $math.enable -}}
    {{- $source := $cdn.katexCSS | default "lib/katex/katex.min.css" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/style.html" -}}
    {{- if not $cdn.katexCSS -}}
        {{- range resources.Match "lib/katex/fonts/*" -}}
            {{- .Publish -}}
        {{- end -}}
    {{- end -}}
    {{- $source := $cdn.katexJS | default "lib/katex/katex.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- $source := $cdn.katexAutoRenderJS | default "lib/katex/contrib/auto-render.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- if $math.copyTex -}}
        {{- $source := $cdn.katexCopyTexJS | default "lib/katex/contrib/copy-tex.min.js" -}}
        {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- end -}}
    {{- if $math.mhchem -}}
        {{- $source := $cdn.katexMhchemJS | default "lib/katex/contrib/mhchem.min.js" -}}
        {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- end -}}
    {{- $delimiters := slice (dict "left" "$$" "right" "$$" "display" true) (dict "left" "\\[" "right" "\\]" "display" true) -}}
    {{- $delimiters = $delimiters | append (dict "left" "\\begin{equation}" "right" "\\end{equation}" "display" true) -}}
    {{- $delimiters = $delimiters | append (dict "left" "\\begin{equation*}" "right" "\\end{equation*}" "display" true) -}}
    {{- $delimiters = $delimiters | append (dict "left" "\\begin{align}" "right" "\\end{align}" "display" true) -}}
    {{- $delimiters = $delimiters | append (dict "left" "\\begin{align*}" "right" "\\end{align*}" "display" true) -}}
    {{- $delimiters = $delimiters | append (dict "left" "\\begin{alignat}" "right" "\\end{alignat}" "display" true) -}}
    {{- $delimiters = $delimiters | append (dict "left" "\\begin{alignat*}" "right" "\\end{alignat*}" "display" true) -}}
    {{- $delimiters = $delimiters | append (dict "left" "\\begin{gather}" "right" "\\end{gather}" "display" true) -}}
    {{- $delimiters = $delimiters | append (dict "left" "\\begin{CD}" "right" "\\end{CD}" "display" true) -}}
    {{- if and $math.blockLeftDelimiter $math.blockRightDelimiter -}}
        {{- $delimiters = $delimiters | append (dict "left" $math.blockLeftDelimiter "right" $math.blockRightDelimiter "display" true) -}}
    {{- end -}}
    {{- $delimiters = $delimiters | append (dict "left" "$" "right" "$" "display" false) (dict "left" "\\(" "right" "\\)" "display" false) -}}
    {{- if and $math.inlineLeftDelimiter $math.inlineRightDelimiter -}}
        {{- $delimiters = $delimiters | append (dict "left" $math.inlineRightDelimiter "right" $math.inlineRightDelimiter "display" false) -}}
    {{- end -}}
    {{- $config = dict "strict" false "delimiters" $delimiters | dict "math" | merge $config -}}
{{- end -}}

{{- /* mermaid */ -}}
{{- if (.Scratch.Get "this").mermaid -}}
    {{- $source := $cdn.mermaidJS | default "lib/mermaid/mermaid.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
{{- end -}}

{{- /* ECharts */ -}}
{{- if (.Scratch.Get "this").echarts -}}
    {{- $source := $cdn.echartsJS | default "lib/echarts/echarts.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- $lightTheme := resources.Get "lib/echarts/theme/light.yml" | transform.Unmarshal -}}
    {{- $darkTheme := resources.Get "lib/echarts/theme/dark.yml" | transform.Unmarshal -}}
    {{- $config = dict "lightTheme" $lightTheme "darkTheme" $darkTheme | dict "echarts" | merge $config -}}
{{- end -}}

{{- /* Mapbox GL */ -}}
{{- if (.Scratch.Get "this").mapbox -}}
    {{- $source := $cdn.mapboxGLCSS | default "lib/mapbox-gl/mapbox-gl.min.css" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/style.html" -}}
    {{- $source := $cdn.mapboxGLJS | default "lib/mapbox-gl/mapbox-gl.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- dict "Source" "lib/mapbox-gl/mapbox-gl-language.js" "Minify" true "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- $config = dict "accessToken" $params.mapbox.accessToken "RTLTextPlugin" "https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-rtl-text/v0.2.0/mapbox-gl-rtl-text.js" | dict "mapbox" | merge $config -}}
{{- end -}}

{{- /* Music */ -}}
{{- if (.Scratch.Get "this").music -}}
    {{- /* APlayer */ -}}
    {{- $source := $cdn.aplayerCSS | default "lib/aplayer/APlayer.min.css" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/style.html" -}}
    {{- $options := dict "targetPath" "lib/aplayer/dark.min.css" "outputStyle" "compressed" "enableSourceMap" true -}}
    {{- dict "Source" "lib/aplayer/dark.scss" "ToCSS" $options "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/style.html" -}}
    {{- /* To fix https://github.com/dillonzq/LoveIt/issues/869 */ -}}
    {{- /* Local file from https://github.com/DIYgod/APlayer/pull/777, no CDN */ -}}
    {{- $source := "lib/aplayer/APlayer.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}

    {{- /* MetingJS */ -}}
    {{- $source := $cdn.metingJS | default "lib/meting/Meting.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
{{- end -}}

{{- /* Cookie Consent */ -}}
{{- if .Site.Params.cookieconsent | and .Site.Params.cookieconsent.enable -}}
    {{- $source := $cdn.cookieconsentCSS | default "lib/cookieconsent/cookieconsent.min.css" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/style.html" -}}
    {{- $source := $cdn.cookieconsentJS | default "lib/cookieconsent/cookieconsent.min.js" -}}
    {{- dict "Source" $source "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}
    {{- $cookieconsentConfig := dict "popup" (dict "background" "#1aa3ff") "button" (dict "background" "#f0f0f0") | dict "theme" "edgeless" "palette" -}}
    {{- $cookieconsentConfig = .Site.Params.cookieconsent | merge $cookieconsentConfig -}}
    {{- $cookieconsentConfig = dict "message" ($cookieconsentConfig.content.message | default (T "cookieconsentMessage")) "dismiss" ($cookieconsentConfig.content.dismiss | default (T "cookieconsentDismiss")) "link" ($cookieconsentConfig.content.link | default (T "cookieconsentLink")) | dict "content" | merge $cookieconsentConfig -}}
    {{- $config = $cookieconsentConfig | dict "cookieconsent" | merge $config -}}
{{- end -}}

{{- range $params.library.css -}}
    {{- dict "Source" . "Fingerprint" $fingerprint | dict "Scratch" $.Scratch "Data" | partial "scratch/style.html" -}}
{{- end -}}

{{- range $params.library.js -}}
    {{- dict "Source" . "Fingerprint" $fingerprint | dict "Scratch" $.Scratch "Data" | partial "scratch/script.html" -}}
{{- end -}}

{{- with (.Scratch.Get "this").styleArr -}}
    {{- $content := delimit . "" -}}
    {{- $path := substr (md5 $content) 0 6 | printf "css/%v" -}}
    {{- $options := printf "%v.min.css" $path | dict "targetPath" | merge (dict "outputStyle" "compressed") -}}
    {{- dict "Content" $content "Path" (printf "%v.scss" $path) "ToCSS" $options | dict "Scratch" $.Scratch "Data" | partial "scratch/style.html" -}}
{{- end -}}

{{- /* Config script */ -}}
{{- $config | jsonify | printf "window.config=%s;" | dict "Content" | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}

{{- /* Theme script */ -}}
{{- dict "Source" "js/theme.js" "Minify" true "Fingerprint" $fingerprint | dict "Scratch" .Scratch "Data" | partial "scratch/script.html" -}}

{{- with (.Scratch.Get "this").scriptArr -}}
    {{- delimit . "\n" | dict "Content" | dict "Scratch" $.Scratch "Data" | partial "scratch/script.html" -}}
{{- end -}}

{{- range (.Scratch.Get "this").style -}}
    {{- partial "plugin/style.html" . -}}
{{- end -}}

{{- range (.Scratch.Get "this").script -}}
    {{- partial "plugin/script.html" . -}}
{{- end -}}

{{- partial "plugin/analytics.html" . -}}

{{- /* Ad Blocker Detection */ -}}
{{- partial "plugin/adblock-detector.html" . -}}

#!/bin/bash

# Test script for Ad Blocker Detection implementation
# This script verifies that all required files are in place and properly configured

echo "🧪 Testing Ad Blocker Detection Implementation"
echo "=============================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Function to check if file exists
check_file() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $description"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}: $description"
        echo -e "   ${YELLOW}Missing file: $file${NC}"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Function to check if content exists in file
check_content() {
    local file="$1"
    local pattern="$2"
    local description="$3"
    
    if [ -f "$file" ] && grep -q "$pattern" "$file"; then
        echo -e "${GREEN}✅ PASS${NC}: $description"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}: $description"
        if [ ! -f "$file" ]; then
            echo -e "   ${YELLOW}File not found: $file${NC}"
        else
            echo -e "   ${YELLOW}Pattern not found: $pattern${NC}"
        fi
        ((TESTS_FAILED++))
        return 1
    fi
}

echo -e "\n${BLUE}📁 Checking Required Files${NC}"
echo "-------------------------"

# Check JavaScript file
check_file "themes/LoveIt/assets/js/adblock-detector.js" "Ad blocker detection JavaScript"

# Check CSS file
check_file "themes/LoveIt/assets/css/adblock-notification.scss" "Ad blocker notification styles"

# Check Hugo partial
check_file "themes/LoveIt/layouts/partials/plugin/adblock-detector.html" "Hugo partial template"

# Check documentation
check_file "ADBLOCK-DETECTION.md" "Documentation file"

echo -e "\n${BLUE}🌐 Checking Translation Files${NC}"
echo "-----------------------------"

# Check English translations
check_content "themes/LoveIt/i18n/en.toml" "adBlockerDetectedTitle" "English translations"

# Check Chinese translations
check_content "themes/LoveIt/i18n/zh-CN.toml" "adBlockerDetectedTitle" "Chinese translations"

echo -e "\n${BLUE}🔧 Checking Integration${NC}"
echo "----------------------"

# Check if adblock detector is integrated into assets.html
check_content "themes/LoveIt/layouts/partials/assets.html" "adblock-detector.html" "Integration with assets.html"

echo -e "\n${BLUE}⚙️ Checking Configuration${NC}"
echo "-------------------------"

# Check if Google AdSense is configured
if [ -f "hugo.yaml" ]; then
    if grep -q "adsense:" "hugo.yaml"; then
        echo -e "${GREEN}✅ PASS${NC}: Google AdSense configuration found"
        ((TESTS_PASSED++))
    else
        echo -e "${YELLOW}⚠️ WARN${NC}: Google AdSense not configured in hugo.yaml"
        echo -e "   ${YELLOW}Ad blocker detection will only work when AdSense is enabled${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ WARN${NC}: hugo.yaml not found"
fi

echo -e "\n${BLUE}📝 Checking File Contents${NC}"
echo "-------------------------"

# Check if JavaScript contains key functions
if [ -f "themes/LoveIt/assets/js/adblock-detector.js" ]; then
    check_content "themes/LoveIt/assets/js/adblock-detector.js" "class AdBlockDetector" "JavaScript class definition"
    check_content "themes/LoveIt/assets/js/adblock-detector.js" "testElementDetection" "Element detection method"
    check_content "themes/LoveIt/assets/js/adblock-detector.js" "testGoogleAdsScript" "Script detection method"
    check_content "themes/LoveIt/assets/js/adblock-detector.js" "testNetworkRequests" "Network detection method"
fi

# Check if CSS contains key styles
if [ -f "themes/LoveIt/assets/css/adblock-notification.scss" ]; then
    check_content "themes/LoveIt/assets/css/adblock-notification.scss" ".adblock-notification" "Main notification styles"
    check_content "themes/LoveIt/assets/css/adblock-notification.scss" "theme=\"dark\"" "Dark theme support"
    check_content "themes/LoveIt/assets/css/adblock-notification.scss" "@media.*max-width" "Responsive design"
fi

echo -e "\n${BLUE}🎯 Test Summary${NC}"
echo "==============="

TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))

echo -e "Total tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"

if [ $TESTS_FAILED -gt 0 ]; then
    echo -e "${RED}Failed: $TESTS_FAILED${NC}"
    echo -e "\n${RED}❌ Some tests failed. Please check the missing files or configurations.${NC}"
    exit 1
else
    echo -e "${RED}Failed: $TESTS_FAILED${NC}"
    echo -e "\n${GREEN}🎉 All tests passed! Ad blocker detection is properly implemented.${NC}"
fi

echo -e "\n${BLUE}📋 Next Steps${NC}"
echo "============="
echo "1. Build your Hugo site: hugo"
echo "2. Test with an ad blocker enabled"
echo "3. Verify the notification appears and is dismissible"
echo "4. Test in both light and dark themes"
echo "5. Check mobile responsiveness"

echo -e "\n${BLUE}🔍 Manual Testing${NC}"
echo "=================="
echo "• Enable an ad blocker (uBlock Origin, AdBlock Plus, etc.)"
echo "• Visit your site and check if the notification appears"
echo "• Verify the notification only shows once per session"
echo "• Test the 'Refresh page after disabling' button"
echo "• Check both Chinese and English language versions"

exit 0

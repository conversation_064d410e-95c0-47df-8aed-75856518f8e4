baseURL: https://blog.wenhaofree.com/
title: WenHaoFree
theme: ["LoveIt"]
# theme: ["LoveItPro"]

# determines default content language ["en", "zh-cn", "fr", "pl", ...]
# 设置默认的语言 ["en", "zh-cn", "fr", "pl", ...]
defaultContentLanguage: "zh-cn"
# language code ["en", "zh-CN", "fr", "pl", ...]
# 网站语言, 仅在这里 CN 大写 ["en", "zh-CN", "fr", "pl", ...]
languageCode: "zh-CN"
# language name ["English", "简体中文", "Français", "Polski", ...]
# 语言名称 ["English", "简体中文", "Français", "Polski", ...]
languageName: "简体中文"
# whether to include Chinese/Japanese/Korean
# 是否包括中日韩文字
hasCJKLanguage: true

# whether to use robots.txt
# 是否使用 robots.txt
enableRobotsTXT: true
# whether to use git commit log
# 是否使用 git 信息
enableGitInfo: true
# whether to use emoji code
# 是否使用 emoji 代码
enableEmoji: true

# Hugo configuration
pagination:
  pagerSize: 12
rssLimit: 20

# Taxonomies configuration
# 分类法配置
taxonomies:
  tag: "tags"
  category: "categories"

# Output formats configuration
# 输出格式配置
outputs:
  home: ["HTML", "RSS", "JSON"]
  page: ["HTML"]
  section: ["HTML", "RSS"]
  taxonomy: ["HTML", "RSS"]
  term: ["HTML", "RSS"]

# Multilingual
# 多语言
languages:
  zh-cn:
    weight: 1
    languageCode: "zh-CN"
    languageName: "简体中文"
    hasCJKLanguage: true
    title: "WenHaoFree"
    menu:
      main:
        - weight: 1
          identifier: "posts"
          name: "所有文章"
          url: "/posts/"
          title: ""
        - weight: 2
          identifier: "tags"
          name: "标签"
          url: "/tags/"
          title: ""
        - weight: 3
          identifier: "categories"
          name: "分类"
          url: "/categories/"
          title: ""
        - weight: 4
          identifier: "plog"
          name: "图册"
          url: "https://plog.wenhaofree.com/zh"
          title: ""
        - weight: 5
          identifier: "about"
          name: "关于"
          url: "/about/"
          title: ""
  en:
    weight: 2
    languageCode: "en"
    languageName: "English"
    hasCJKLanguage: false
    title: "WenHaoFree"
    menu:
      main:
        - weight: 1
          identifier: "posts"
          name: "All Articles"
          url: "/posts/"
          title: ""
        - weight: 2
          identifier: "tags"
          name: "Tags"
          url: "/tags/"
          title: ""
        - weight: 3
          identifier: "categories"
          name: "Categories"
          url: "/categories/"
          title: ""
        - weight: 4
          identifier: "plog"
          name: "Plog"
          url: "https://plog.wenhaofree.com/zh"
          title: ""
        - weight: 5
          identifier: "about"
          name: "About"
          url: "/about/"
          title: ""

# Theme Parameters
params:
  # Site Information
  author:
    name: "WenHao"
    email: "<EMAIL>"
  email: "<EMAIL>"
  description: "文浩的个人博客 - 分享技术与生活"

  # Theme Configuration
  defaultTheme: "auto" # auto, light, dark

  # Date format
  dateFormat: "2006-01-02"

  # Website title for Open Graph and Twitter Cards
  title: "WenHaoFree"

  # Website images for Open Graph and Twitter Cards
  images: ["/images/logo.png"]

  # Header Configuration
  header:
    enable: true
    carousel:
      enable: true  # 启用轮播图
      autoplay: true
      interval: 5000
      showIndicators: true
      showControls: true
    search:
      enable: true
      placeholder: "搜索文章..."
      maxResults: 10

  # Home Page Configuration
  home:
    featured:
      enable: true
      title: "精选文章"
      count: 3
    posts:
      enable: true
      title: "最新文章"
      count: 10
      paginate: 10

  # Article Configuration
  article:
    card:
      enable: true
      showImage: true
      showExcerpt: true
      showTags: true
      showReadingTime: true
      showDate: true
      showAuthor: true
    readingTime:
      enable: true
      wordsPerMinute: 200

  # Sidebar Configuration
  sidebar:
    enable: true
    position: "right"
    about:
      enable: true
      title: "关于博主"
      description: "欢迎来到我的博客"
    popular:
      enable: true
      title: "热门文章"
      count: 5
    newsletter:
      enable: true
      title: "订阅博客"
      description: "获取最新文章推送"
    social:
      enable: true
      title: "关注我"

  # Social Media Links
  social:
    GitHub: "wenhaofree"
    Email: "<EMAIL>"
    RSS: true

  # Comment System
  comments:
    enable: false
    provider: "disqus"

  # SEO Configuration
  seo:
    enable: true
    image: "/images/logo.png"
    twitterCard: "summary_large_image"

  # Analytics Configuration
  analytics:
    # Google Analytics (已配置)
    google:
      id: "G-TBMXJKG84N"  # 您的 GA4 ID
      respectDoNotTrack: false

    # Google AdSense (新增)
    adsense:
      enable: true   # 启用 AdSense
      client: "ca-pub-****************"  # 您的 AdSense 客户端 ID
      autoAds: true   # 启用自动广告

      # 展示广告配置
      displayAds:
        - slot: "ca-pub-****************"  # 替换为您的实际广告位 ID
          display: "block"
          format: "auto"
          responsive: true
          margin: "20px 0"

        - slot: "ca-pub-****************"  # 替换为您的实际广告位 ID
          display: "block"
          format: "rectangle"
          style: "width: 300px; height: 250px;"
          margin: "30px auto"

      # 信息流广告
      infeedAds:
        - slot: "ca-pub-****************"  # 替换为您的实际广告位 ID
          layoutKey: "-6t+ed+2i-1n-4w"  # 替换为您的布局密钥
          margin: "20px 0"

      # 文章内广告
      inarticleAds:
        - slot: "ca-pub-****************"  # 替换为您的实际广告位 ID
          margin: "30px 0"

    # Umami Analytics (新增)
    umami:
      enable: true   # 启用 Umami 跟踪
      server: "https://umami.wenhaofree.com"
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"
      respectDoNotTrack: false
      autoTrack: "true"
      doNotTrack: "false"
      cache: "false"

  # Asset Configuration
  assets:
    framework: "bootstrap"

  # Color Configuration
  colors:
    primary: "#007bff"
    secondary: "#6c757d"
    success: "#28a745"
    danger: "#dc3545"
    warning: "#ffc107"
    info: "#17a2b8"
    light: "#f8f9fa"
    dark: "#343a40"

  # Font Configuration
  fonts:
    sansSerif: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
    serif: "Georgia, 'Times New Roman', Times, serif"
    monospace: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"
    size:
      base: "1rem"
      lg: "1.25rem"
      sm: "0.875rem"

  # Design Configuration
  design:
    borderRadius: "0.375rem"
    boxShadow: "0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)"
    transition: "all 0.15s ease-in-out"

  # Page global config
  page:
    # Table of contents config
    toc:
      enable: true
      keepStatic: false
      auto: true
    # Code config
    code:
      copy: true
      maxShownLines: 50
    # Social share links in post page
    share:
      enable: true
      X: true
      Facebook: true
      Linkedin: false
      Whatsapp: false
      Pinterest: false
      Tumblr: false
      HackerNews: true
      Reddit: false
      VK: false
      Buffer: false
      Xing: false
      Line: true
      Instapaper: false
      Pocket: false
      Flipboard: false
      Weibo: true
      Blogger: false
      Baidu: false
      Odnoklassniki: false
      Evernote: false
      Skype: false
      Trello: false
      Diaspora: true
      Mix: false
      Telegram: true
  

    slot: "****************"



---
title: "广告拦截检测演示页面"
date: 2025-06-26T10:00:00+08:00
draft: false
categories: ["测试"]
tags: ["广告检测", "演示"]
description: "演示广告拦截检测功能的测试页面"
---

# 广告拦截检测演示

这是一个专门用来演示广告拦截检测功能的页面。

## 🎯 测试说明

### 正常情况（无广告拦截器）
- 页面会正常显示 Google AdSense 广告
- 不会出现任何弹窗提示
- 广告位会显示实际的广告内容

### 启用广告拦截器时
- 广告位会被隐藏或显示空白
- 系统会自动检测到广告被拦截
- 弹出友好的提示信息：**"Please allow ads on our site. Looks like you're using an ad blocker. We rely on advertising to help fund our site."**

## 📱 测试步骤

1. **无广告拦截器测试**：
   - 确保浏览器没有启用广告拦截器
   - 刷新此页面
   - 观察广告是否正常显示

2. **广告拦截器测试**：
   - 启用广告拦截器（如 uBlock Origin、AdBlock Plus 等）
   - 刷新此页面
   - 观察是否出现广告拦截检测提示

3. **多语言测试**：
   - 切换到英文版本测试英文提示
   - 切换到中文版本测试中文提示

## 🔧 技术实现

我们的广告拦截检测系统使用了三种检测方法：

### 1. 元素检测
创建具有广告相关类名的测试元素，检查是否被隐藏

### 2. 脚本加载检测  
监控 Google Ads 脚本是否成功加载

### 3. 网络请求检测
测试对广告服务器的请求是否被阻止

## 📊 广告位展示区域

下面是一些广告位的展示区域。如果您没有启用广告拦截器，这些区域会显示实际的广告：

---

### 横幅广告区域
{{< adsense slot="1234567890" format="auto" responsive="true" >}}

---

### 矩形广告区域
{{< adsense slot="0987654321" format="rectangle" style="display:block; width:300px; height:250px;" responsive="false" >}}

---

### 自适应广告区域
{{< adsense slot="1111111111" format="fluid" responsive="true" >}}

---

### 测试元素（会被广告拦截器隐藏）

下面这些元素使用了广告拦截器通常会拦截的类名，用于测试检测功能：

<div class="adsbox" style="width: 300px; height: 250px; border: 2px solid #ff6b6b; margin: 20px auto; padding: 20px; text-align: center; background-color: #ffe0e0;">
    <p><strong>测试元素 1</strong></p>
    <p>类名: adsbox</p>
    <p>如果您启用了广告拦截器，这个元素可能会被隐藏</p>
</div>

<div class="advertisement" style="width: 728px; height: 90px; border: 2px solid #4ecdc4; margin: 20px auto; padding: 20px; text-align: center; background-color: #e0f7f5; max-width: 100%;">
    <p><strong>测试元素 2</strong></p>
    <p>类名: advertisement</p>
    <p>如果您启用了广告拦截器，这个元素可能会被隐藏</p>
</div>

<div class="pub_300x250" style="width: 300px; height: 250px; border: 2px solid #45b7d1; margin: 20px auto; padding: 20px; text-align: center; background-color: #e0f2ff;">
    <p><strong>测试元素 3</strong></p>
    <p>类名: pub_300x250</p>
    <p>如果您启用了广告拦截器，这个元素可能会被隐藏</p>
</div>

---

## ⚠️ 重要说明

1. **广告位 ID 配置**：当前配置使用的是示例广告位 ID，您需要将其替换为您在 Google AdSense 中创建的实际广告位 ID。

2. **检测灵敏度**：系统需要至少 2 种检测方法确认广告被拦截才会显示提示，这样可以减少误报。

3. **会话管理**：提示信息在每个浏览器会话中只会显示一次，避免重复打扰用户。

## 🛠️ 配置说明

在您的 `hugo.yaml` 文件中，广告配置如下：

```yaml
params:
  analytics:
    adsense:
      enable: true
      client: "ca-pub-5455845858903578"
      autoAds: true
      displayAds:
        - slot: "您的广告位ID"
          display: "block"
          format: "auto"
          responsive: true
```

## 📞 技术支持

如果您在测试过程中遇到任何问题：

1. 检查浏览器控制台是否有错误信息
2. 确认 Google AdSense 配置正确
3. 验证广告位 ID 是否有效
4. 测试不同的广告拦截器

---

**提示**：这个演示页面帮助您验证广告拦截检测功能是否正常工作。在实际使用中，您可以删除这个测试页面。

---
title: "广告拦截测试页面"
date: 2025-06-26T10:30:00+08:00
draft: false
categories: ["测试"]
tags: ["广告拦截", "测试"]
description: "简单的广告拦截检测测试页面"
---

# 广告拦截检测测试

这是一个简单的测试页面，用来验证广告拦截检测功能。

## 🎯 测试说明

1. **正常情况**：如果您没有启用广告拦截器，页面会正常显示，不会有弹窗
2. **广告拦截器启用**：如果您启用了 AdBlock Plus、uBlock Origin 等广告拦截器，页面会在 2-3 秒后弹出提示

## 📱 如何测试

### 步骤 1：无广告拦截器测试
- 确保浏览器没有启用广告拦截器
- 刷新此页面
- 应该不会看到任何弹窗

### 步骤 2：广告拦截器测试
- 安装并启用广告拦截器（推荐 uBlock Origin）
- 刷新此页面
- 等待 2-3 秒，应该会看到弹窗提示

## 🔧 测试元素

下面这些元素会被广告拦截器隐藏，用于检测：

<div class="adsbox" style="width: 300px; height: 100px; border: 2px solid red; margin: 20px auto; padding: 20px; text-align: center; background: #ffe0e0;">
    <strong>测试元素 1</strong><br>
    类名: adsbox<br>
    如果被隐藏说明广告拦截器生效
</div>

<div class="advertisement" style="width: 300px; height: 100px; border: 2px solid blue; margin: 20px auto; padding: 20px; text-align: center; background: #e0e0ff;">
    <strong>测试元素 2</strong><br>
    类名: advertisement<br>
    如果被隐藏说明广告拦截器生效
</div>

<div class="pub_300x250" style="width: 300px; height: 100px; border: 2px solid green; margin: 20px auto; padding: 20px; text-align: center; background: #e0ffe0;">
    <strong>测试元素 3</strong><br>
    类名: pub_300x250<br>
    如果被隐藏说明广告拦截器生效
</div>

## 📊 实际广告位

这里是实际的 Google AdSense 广告位：

{{< adsense slot="ca-pub-5455845858903578" format="auto" responsive="true" >}}

---

## ⚠️ 预期结果

- **无广告拦截器**：所有测试元素正常显示，广告位显示占位符，无弹窗
- **有广告拦截器**：测试元素可能被隐藏，2-3秒后出现检测弹窗

## 🔍 调试信息

如果检测不工作，请：

1. 打开浏览器开发者工具（F12）
2. 查看 Console 标签页是否有错误
3. 查看 Network 标签页确认 JS/CSS 文件已加载
4. 确认您使用的是常见的广告拦截器

---

**提示**：这个页面专门用于测试广告拦截检测功能。如果一切正常，您应该能看到相应的检测效果。
